import React from "react";
import { HiBars3, HiTrash } from "react-icons/hi2";
import { IoShirtOutline } from "react-icons/io5";
import { useAuth } from "../../contexts/AuthContext";
import { usePicks } from "../../contexts/PicksContext";
import { getConfidenceColor } from "../../utils/colorUtils";

function HomePage() {
  const { navigateToView } = useAuth();
  const { getPicks, removePick, totalPicksCount } = usePicks();
  const userPicks = getPicks();

  const handleAddPicksClick = () => {
    navigateToView("addPicks");
  };

  return (
    <div className="min-h-screen bg-[#061844] text-[#58C612] flex flex-col items-center justify-center p-4 select-none">
      <div className="absolute top-15 left-10 sm:top-15 sm:left-15">
        <HiBars3 className="w-auto h-14 sm:h-16 text-white bg-[#233e6c] rounded-lg hover:text-white transition-colors duration-300 cursor-pointer" />
      </div>

      <div className="w-full h-[100px] flex justify-center mb-auto mt-5 mt-10">
        <header className="flex items-center justify-center">
          <img
            src="/project_parlay_logo.png"
            alt="Project Parlay Logo"
            className="w-auto h-32 sm:h-40 select-none transition-all duration-300 ease-in-out"
          />
        </header>
      </div>

      {totalPicksCount === 0 ? (
        // Empty state
        <div className="text-center mx-auto mb-auto mt-[-25%] sm:mt-[-10%] ">
          <h2 className="text-white text-[400%] font-bold text-center">
            Time for a fresh start
          </h2>

          <p className="text-white text-[125%] sm:text-[200%] mb-8">
            You don't have any picks in your list
          </p>

          <button
            onClick={handleAddPicksClick}
            className="px-8 py-4 bg-[#233e6c] hover:bg-[#232d6c] text-white w-[60%] font-bold rounded-lg text-[100%] text-[150%] sm:text-[200%] transition-all ease-linear duration-300 shadow-lg hover:shadow-xl hover:cursor-pointer"
          >
            Add Picks
          </button>
        </div>
      ) : (
        // My Picks display
        <div className="w-full max-w-7xl mx-auto px-4 mb-auto mt-4">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-white text-3xl sm:text-4xl font-bold">
              My Picks ({totalPicksCount})
            </h2>
            <button
              onClick={handleAddPicksClick}
              className="px-6 py-3 bg-[#233e6c] hover:bg-[#1a2d54] text-white font-bold rounded-lg text-lg transition-all ease-linear duration-300 shadow-lg hover:shadow-xl hover:cursor-pointer"
            >
              Add More Picks
            </button>
          </div>

          {/* 2-column grid of picks */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {userPicks.map((pick) => (
              <div
                key={pick.id}
                className="bg-[#233e6c] rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 ease-linear"
              >
                <div className="flex items-center gap-4 mb-4">
                  {/* Player info */}
                  <div className="flex items-center gap-4 flex-1">
                    <div
                      className="w-16 h-16 rounded-full flex items-center justify-center bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden"
                      style={{
                        border: `3px solid ${getConfidenceColor(pick.confidence || 75)}`,
                      }}
                    >
                      <IoShirtOutline
                        className="w-10 h-10 absolute"
                        style={{ color: getConfidenceColor(pick.confidence || 75) }}
                      />
                      <div className="text-white font-bold text-sm z-10 relative">
                        {pick.playerNumber}
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-white text-lg font-bold">
                        {pick.playerName}
                      </h3>
                      <p className="text-gray-300 text-sm">{pick.betType}</p>
                      <p className="text-gray-400 text-xs">{pick.gameInfo}</p>
                      {pick.handicapperName && (
                        <p className="text-blue-400 text-xs mt-1">
                          Recommended by {pick.handicapperName}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Confidence and remove button */}
                  <div className="flex flex-col items-center gap-2">
                    <div className="text-center">
                      <div
                        className="text-3xl font-bold"
                        style={{ color: getConfidenceColor(pick.confidence || 75) }}
                      >
                        {pick.confidence || 75}
                      </div>
                      <div className="text-white text-xs font-medium">
                        Confidence
                      </div>
                    </div>
                    <button
                      onClick={() => removePick(pick.id)}
                      className="p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all duration-300 hover:scale-105 shadow-md hover:shadow-lg hover:cursor-pointer"
                      title="Remove pick"
                    >
                      <HiTrash className="w-4 h-4 hover:cursor-pointer" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default HomePage;
