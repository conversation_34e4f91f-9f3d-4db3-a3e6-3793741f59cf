<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Project Parlay</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='Frontend/how_to_bet.css') }}">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            max-width: 700px;
        }

        #popupForm, #boostForm, #protectedForm {
            display: none;
            background: white;
            padding: 20px;
            border: 2px solid #000;
            position: fixed;
            top: 5%;
            left: 30%;
            z-index: 999;
            box-shadow: 0 0 20px rgba(0,0,0,0.3);
            max-height: 85vh;             
            overflow-y: auto;             /* scroll vertically if needed */
            width: 500px;                 
        }

        .form-group {
            margin-bottom: 20px;
        }

        .button-stack {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        }

        button {
            padding: 8px 12px;
            font-size: 14px;
        }

        label {
            display: block;
            margin-top: 10px;
        }

        input[type="text"],
        input[type="number"] {
            padding: 5px;
            width: 100%;
        }

        #subparlayGrid {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            max-width: 100%; /* ensures it wraps instead of overflowing */
            padding-top: 10px;
        }

        .pick-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .pick-button {
            background-color: #d9c68f;
            padding: 10px;
            border: 1px solid black;
            position: relative;
            width: 70%;
            min-width: 400px;
            display: flex;
            align-items: center;
            font-family: monospace;
            font-size: 14px;
            justify-content: space-between;
        }

        .bar-container {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 5px;
            width: 100%;
            display: flex;
        }

        .bar {
            height: 100%;
        }

        .bar.purple {
            background-color: purple;
        }

        .bar.gold {
            background-color: gold;
        }

        .pick-text {
            width: 100%;
            padding-right: 10px;
        }

        .edit-btn, .delete-btn {
            margin-left: 10px;
            padding: 10px 15px;
            font-size: 14px;
        }

    </style>
</head>
<body>

    <div id="accuracyBox" class="form-group">
        <p><strong>User Accuracy:</strong> <span id="accuracyValue">{{ user_accuracy }}</span></p>
        <input type="text" id="accuracyInput" placeholder="Enter accuracy %">
        <button onclick="updateAccuracy()">Update</button>
    </div>

    <div style="margin-top: 10px;">
        <label for="totalCapitalInput"><strong>Total Capital ($):</strong></label>
        <input type="number" id="totalCapitalInput" placeholder="Enter total bankroll" style="margin-left: 10px; width: 150px;">
    </div>

    <h1>Project Parlay</h1>

    <h4>Enter Pick Name (eg. Mike Trout 0.5 HR)</h4>

    <div class="form-group">
        <input type="text" id="userInput" placeholder="Enter name here">
    </div>

    <div class="button-stack">
        <button onclick="openPopup()">Create Pick</button>
        <button onclick="openBoostForm()">New Boost Promo</button>
        <button onclick="openProtectedForm()">New Protected Play Promo</button>
        <button onclick="runOptimization()">Run Optimizer</button>
        <button onclick="loadSamplePicks()">Load Sample Picks</button>
        <button onclick="clearPicks()">Clear All Picks</button>
        <button onclick="submitVerifiedPicks()">Submit Verified Picks</button>

        <p id="optimizerResult"></p>

    </div>

    <h3>Generated Subparlays</h3>
    <div id="subparlayGrid"></div>


    <p><strong>Server Response:</strong> <span id="responseOutput"></span></p>

    <div id="objectButtonsContainer"></div>

    <p id="optimizerResult"><strong>Optimizer Result:</strong> <span id="optimizerSummary"></span></p>

    <!-- Pick Form -->
    <div id="popupForm">
        <p><strong>Name:</strong> <span id="popupNameDisplay"></span></p>

        <label><strong>Prediction:</strong></label>
        <label><input type="radio" name="globalPrediction" value="1" checked> Higher </label>
        <label><input type="radio" name="globalPrediction" value="0"> Lower </label>
        <label>Odds: <input type="text" id="popupOdds"></label>

        <label><strong>Pick Type:</strong></label>
        <label><input type="checkbox" name="pickType" value="MoneyLine" onchange="handlePickTypeCheck(this)"> MoneyLine</label>
        <label><input type="checkbox" name="pickType" value="Prop" onchange="handlePickTypeCheck(this)"> Prop</label>

        <!-- Hidden input shown when Prop is selected -->
        <div id="playerTeamDiv" style="display: none; margin-top: 10px;">
            <label>Player Team: <input type="text" id="playerTeamInput" placeholder="e.g. Lakers"></label>
        </div>

        <label>Pick Origin:</label>
        <label><input type="checkbox" name="pickOrigin" value="ChalkBoardPI"> ChalkBoard PI</label>
        <label><input type="checkbox" name="pickOrigin" value="HarryLock"> HarryLock</label>
        <label><input type="checkbox" name="pickOrigin" value="DanGamblePOD"> DanGamble POD</label>
        <label><input type="checkbox" name="pickOrigin" value="DanGambleAIEdge"> Dan GambleAI Edge</label>
        <label><input type="checkbox" name="pickOrigin" value="GameScript"> GameScript</label>
        <label><input type="checkbox" name="pickOrigin" value="Winible"> Winible</label>
        <label><input type="checkbox" name="pickOrigin" value="DoberMan"> DoberMan</label>
        <label><input type="checkbox" name="pickOrigin" value="JoshMiller"> JoshMiller</label>
        <label><input type="checkbox" name="pickOrigin" value="Me"> Me</label>

        <label>League:</label>
        <label><input type="checkbox" name="league" value="MLB" onclick="handleLeagueExclusive(this)"> MLB</label>
        <label><input type="checkbox" name="league" value="NFL" onclick="handleLeagueExclusive(this)"> NFL</label>
        <label><input type="checkbox" name="league" value="NBA" onclick="handleLeagueExclusive(this)"> NBA</label>
        <label><input type="checkbox" name="league" value="NHL" onclick="handleLeagueExclusive(this)"> NHL</label>

        <!-- Stat Type Section (appears dynamically) -->
        <div id="statTypeSection" style="margin-top: 20px;"></div>

        <label><input type="checkbox" id="popupReusable" checked> Reusable</label>

        <select id="popupMutualExclusion">
            <option value="-1" selected>None (-1)</option>
            <option value="1">1</option>
            <option value="2">2</option>
            ...
            <option value="10">10</option>
        </select>

        <label>Capital Limit: <input type="number" id="popupCapitalLimit" value="0" min="0"></label>

        <br><br>
        <button onclick="submitObject()">Submit</button>
        <button onclick="closePopup()">Cancel</button>
    </div>

    <!-- Boost Promo Form -->
    <div id="boostForm">
        <p><strong>Create Boost Promo</strong></p>
        <label>Boost %: <input type="number" id="boostPercentage" min="1" max="100"></label>
        <label>Required Picks: <input type="number" id="requiredPicks" min="1"></label>
        <label><input type="checkbox" id="sameSport"> Same Sport Restriction</label>
        <br><br>
        <button onclick="submitBoostPromo()">Submit</button>
        <button onclick="closeBoostForm()">Cancel</button>
    </div>

    <!-- Protected Play Promo Form -->
    <div id="protectedForm">
        <p><strong>Create Protected Play Promo</strong></p>
        <label>Protected Amount: <input type="number" id="protectedAmount" min="1"></label>
        <label>Eligible Leagues:</label>
        <label><input type="checkbox" name="eligibleLeague" value="MLB"> MLB</label>
        <label><input type="checkbox" name="eligibleLeague" value="NFL"> NFL</label>
        <label><input type="checkbox" name="eligibleLeague" value="NBA"> NBA</label>
        <label><input type="checkbox" name="eligibleLeague" value="NHL"> NHL</label>
        <label><input type="checkbox" value="ALL" onclick="selectAllEligibleLeagues(this)"> ALL</label>
        <br><br>
        <button onclick="submitProtectedPromo()">Submit</button>
        <button onclick="closeProtectedForm()">Cancel</button>
    </div>

    <!-- JavaScript -->
    <script>
        let tempName = "";
        let editingPickId = null;
        let isEditing = false;

        const statTypeOptions = {
            MLB: ["K's", "Total Bases", "Hitter Fpts", "Pitcher Fpts", "H+R+RBIs", "HR's", "Hits Allowed", "SB's", "Doubles", "Walks Allowed", "Singles", "Walks", "Hits", "Earned Runs Allowed", "RBI's", "Runs"],
            NBA: ["Points", "PRA", "Rebounds", "Assists", "3-pt's", "P+A", "FG Made", "D Rebounds", "Fpts", "R+A", "O Rebounds", "3-pt's Attempted", "FT's Made", "FG Attempted", "P+R", "Dunks", "Blocks", "Steals", "B+S", "Turnovers"],
            NHL: ["Goals", "Assists", "Points", "Shots on Goal", "Power Play Points", "Hits", "Blocked Shots", "Penalty Minutes", "Faceoff Wins", "Time on Ice", "Plus/Minus", "Shots", "Power Play Goals", "Shorthanded Goals", "Game-Winning Goals", "Overtime Goals", "First Goal", "Last Goal", "Anytime Goal", "Multi-Point Game", "Multi-Goal Game"],
            NFL: ["Passing Yards", "Passing Touchdowns", "Interceptions Thrown", "Completions", "Pass Attempts", "Rushing Yards", "Rushing Touchdowns", "Carries", "Receiving Yards", "Receptions", "Receiving Touchdowns", "Targets", "Longest Reception", "Longest Rush", "Total Touchdowns", "Anytime Touchdown", "First Touchdown", "Last Touchdown", "Field Goals Made", "Field Goals Attempted", "Extra Points Made", "Tackles", "Sacks", "Interceptions", "Passes Defended", "Fumbles Recovered", "Forced Fumbles", "Defensive Touchdowns"]
        };

        function attachStatTypeCheckboxLogic() {
            document.querySelectorAll("input[name='statType']").forEach(cb => {
                cb.addEventListener("change", function () {
                    if (this.checked) {
                        document.querySelectorAll("input[name='statType']").forEach(other => {
                            if (other !== this) other.checked = false;
                        });
                    }
                });
            });
        }


        function handlePickTypeCheck(clicked) {
            const checkboxes = document.querySelectorAll("input[name='pickType']");
            checkboxes.forEach(cb => {
                if (cb !== clicked) cb.checked = false;
            });

            const isProp = clicked.value === "Prop" && clicked.checked;
            document.getElementById("playerTeamDiv").style.display = isProp ? "block" : "none";

            // Re-render stat type if switching to Prop and league is selected
            const selectedLeague = document.querySelector("input[name='league']:checked")?.value;
            if (isProp && selectedLeague) {
                renderStatTypeOptions(selectedLeague);
            } else {
                document.getElementById("statTypeSection").innerHTML = "";
            }
        }

        function handleLeagueExclusive(cb) {
            // Uncheck other leagues
            if (cb.checked) {
                document.querySelectorAll("input[name='league']").forEach(other => {
                    if (other !== cb) other.checked = false;
                });

                // Only render stat type options if "Prop" is selected
                const pickType = document.querySelector("input[name='pickType']:checked")?.value;
                if (pickType === "Prop") {
                    renderStatTypeOptions(cb.value);
                } else {
                    document.getElementById("statTypeSection").innerHTML = "";
                }

            } else {
                document.getElementById("statTypeSection").innerHTML = "";
            }
        }

        function attachConfidenceListeners() {
            document.querySelectorAll("input[name='pickOrigin']").forEach(cb => {
                cb.addEventListener("change", function () {
                    const container = this.parentNode;

                    if (this.checked) {
                        // Add only the confidence input
                        let confInput = container.querySelector("input.origin-confidence");
                        if (!confInput) {
                            confInput = document.createElement("input");
                            confInput.type = "number";
                            confInput.placeholder = "Confidence %";
                            confInput.className = "origin-confidence";
                            confInput.style.marginLeft = "8px";
                            confInput.style.width = "80px";
                            confInput.min = "0";
                            confInput.max = "100";
                            container.appendChild(confInput);
                        }

                    } else {
                        // Remove only the confidence input
                        const confInput = container.querySelector("input.origin-confidence");
                        if (confInput) confInput.remove();
                    }
                });
            });
        }


        function renderStatTypeOptions(league) {
            const container = document.getElementById("statTypeSection");
            container.innerHTML = "";

            const stats = statTypeOptions[league];

            if (!stats || !Array.isArray(stats)) {
                container.innerHTML = `<p style="color: red;">No stat options available for ${league}</p>`;
                return;
            }

            const heading = document.createElement("label");
            heading.innerHTML = "<strong>Stat Type:</strong>";
            container.appendChild(heading);
            container.appendChild(document.createElement("br"));

            stats.forEach(stat => {
                const label = document.createElement("label");
                label.style.display = "inline-block";
                label.style.marginRight = "10px";

                const input = document.createElement("input");
                input.type = "checkbox";
                input.name = "statType";
                input.value = stat;

                // Append input and label text
                label.appendChild(input);
                label.append(` ${stat}`);
                container.appendChild(label);
            });

            // Make sure only one checkbox can be selected
            attachStatTypeCheckboxLogic();

            // Optional: log to console for debugging
            console.log(`Stat Type checkboxes rendered for league: ${league}`);
        }


        function submitVerifiedPicks() {
            const verified = [];

            document.querySelectorAll("button[data-verified]").forEach(button => {
                const id = parseInt(button.dataset.pickId);
                const result = parseInt(button.dataset.verified);
                verified.push({ id, actual_result: result });
            });

            console.log("Submitting verified picks:", verified);

            fetch("/submit_verified", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ verified })
            })
            .then(res => res.json())
            .then(data => {
                alert(data.message || "Verification submitted.");
            });
        }




        function openPopup() {
            const input = document.getElementById("userInput").value.trim();
            if (!input) return alert("Please enter a name first.");
            tempName = input;

            // Reset all input fields
            document.getElementById("popupNameDisplay").innerText = input;
            document.getElementById("popupOdds").value = "";
            document.getElementById("playerTeamInput").value = "";
            document.getElementById("popupCapitalLimit").value = 0;
            document.getElementById("popupReusable").checked = true;
            document.getElementById("popupMutualExclusion").value = "-1";

            // Uncheck all checkboxes
            document.querySelectorAll("#popupForm input[type='checkbox']").forEach(cb => cb.checked = false);

            // Clear any stat type options
            document.getElementById("statTypeSection").innerHTML = "";

            // Show the form
            document.getElementById("popupForm").style.display = "block";

            document.getElementById("popupNameDisplay").innerText = input;
            document.querySelectorAll("input[name='pickOrigin'], input[name='league']").forEach(cb => cb.checked = false);
            document.getElementById("popupForm").style.display = "block";
            attachConfidenceListeners();
            const selectedPickType = document.querySelector("input[name='pickType']:checked");
            if (selectedPickType) handlePickTypeCheck(selectedPickType);
        }

        function closePopup() {
            document.getElementById("popupForm").style.display = "none";
        }

        function openBoostForm() {
            document.getElementById("boostForm").style.display = "block";
        }

        function displaySubparlays(subparlays) {
            const grid = document.getElementById("subparlayGrid");
            grid.innerHTML = "";

            if (!Array.isArray(subparlays) || subparlays.length === 0) {
                grid.innerText = "No subparlays generated.";
                return;
            }

            subparlays.forEach((column, i) => {
                const wrapper = document.createElement("div");

                // === Determine Top Label Logic ===
                let topLabelText = "Standard"; // default
                if (column.length >= 3) {
                    const oddsList = column.map(p => parseFloat(p.odds));
                    const meanOdds = oddsList.reduce((a, b) => a + b, 0) / oddsList.length;
                    let votes = { Flex: 0, Standard: 0 };

                    oddsList.forEach(odds => {
                        if (odds - meanOdds >= 0.12) votes.Flex++;
                        else if (meanOdds - odds >= 0.12) votes.Standard++;
                    });

                    if (votes.Flex > votes.Standard) topLabelText = "Flex";
                    else topLabelText = "Standard";
                }

                // === Top Label ===
                const topLabel = document.createElement("div");
                topLabel.innerText = topLabelText;
                topLabel.style.fontWeight = "bold";
                topLabel.style.fontSize = "11px";
                topLabel.style.textAlign = "center";
                topLabel.style.marginBottom = "2px";
                wrapper.appendChild(topLabel);

                // === Picks Column ===
                const colDiv = document.createElement("div");
                colDiv.style.display = "flex";
                colDiv.style.flexDirection = "column";
                colDiv.style.border = "2px solid black";
                colDiv.style.padding = "2px";
                colDiv.style.width = "20px";
                colDiv.style.marginBottom = "2px";

                column.forEach(pick => {
                    const square = document.createElement("div");
                    square.innerText = `#${pick.pID}`;
                    square.style.backgroundColor = getConfidenceColor(pick.confidence || 0);
                    square.style.color = "white";
                    square.style.width = "100%";
                    square.style.height = "7px";
                    square.style.fontSize = "10px";
                    square.style.display = "flex";
                    square.style.alignItems = "center";
                    square.style.justifyContent = "center";
                    square.style.border = "1px solid #ccc";
                    square.style.marginBottom = "2px";
                    colDiv.appendChild(square);
                });

                wrapper.appendChild(colDiv);

                // === Bottom Label ===
                const bottomLabel = document.createElement("div");
                bottomLabel.innerText = "None"; // placeholder
                bottomLabel.style.fontSize = "10px";
                bottomLabel.style.textAlign = "center";
                bottomLabel.style.marginTop = "2px";
                wrapper.appendChild(bottomLabel);

                grid.appendChild(wrapper);
            });

            const pickType = document.querySelector("input[name='pickType']:checked")?.value;
            const league = document.querySelector("input[name='league']:checked")?.value;
            if (pickType === "Prop" && league) {
                renderStatTypeOptions(league);
            }
        }






        function runOptimization() {
        fetch("/optimize_split")
        .then(res => res.json())
        .then(data => {
            // Display the optimizer result summary
            document.getElementById("optimizerSummary").innerText =
                `${data.best_config} | Score: ${data.best_score}`;

            // Rebuild the sorted picks visually with split marker
            displaySortedPicks(data.sorted_picks, data.split_index || -1);

            // ✅ Display the semi-grid of subparlays
            displaySubparlays(data.subparlays);
        });
        }



        function displaySortedPicks(picks, splitIndex) {
            const container = document.getElementById("objectButtonsContainer");
            container.innerHTML = "";

            picks.forEach((obj, index) => {
                if (index === splitIndex) {
                    const divider = document.createElement("div");
                    divider.style.borderTop = "3px solid red";
                    divider.style.margin = "10px 0";
                    container.appendChild(divider);
                }

                const wrapper = document.createElement("div");
                wrapper.style.display = "flex";
                wrapper.style.alignItems = "center";
                wrapper.style.gap = "8px";

                const conf = obj.confidence !== null && obj.confidence !== undefined ? obj.confidence : "N/A";
                const odds = obj.odds || "N/A";
                const name = obj.name || "Unnamed";
                const id = obj.pID

                // 🔧 FIX: Define originLabel based on pick_origin structure
                let originLabel = Array.isArray(obj.pick_origin) ? obj.pick_origin.join(", ") : obj.pick_origin;
                if (Array.isArray(obj.pick_origin)) {
                    if (typeof obj.pick_origin[0] === "object" && obj.pick_origin[0] !== null) {
                        originLabel = obj.pick_origin.map(o => o.name).join(", ");
                    } else {
                        originLabel = obj.pick_origin.join(", ");
                    }
                }

                const btn = document.createElement("button");
                btn.innerText = `[${id}] | ${name} | ${odds} | → Score: ${conf} `;
                btn.style.backgroundColor = getConfidenceColor(obj.confidence);
                wrapper.appendChild(btn);

                const verifyBtn = document.createElement("button");
                verifyBtn.innerText = "Verify: 0";
                verifyBtn.dataset.verified = "0";
                verifyBtn.dataset.pickId = obj.id;
                verifyBtn.style.backgroundColor = "#a83232";
                verifyBtn.style.color = "white";
                verifyBtn.style.padding = "5px";

                verifyBtn.onclick = () => {
                    const current = verifyBtn.dataset.verified === "1";
                    verifyBtn.dataset.verified = current ? "0" : "1";
                    verifyBtn.innerText = `Verify: ${verifyBtn.dataset.verified}`;
                    verifyBtn.style.backgroundColor = current ? "#a83232" : "#28db3f";
                };

                wrapper.appendChild(verifyBtn);
                container.appendChild(wrapper);
            });
        }



        function closeBoostForm() {
            document.getElementById("boostForm").style.display = "none";
        }

        function openProtectedForm() {
            document.getElementById("protectedForm").style.display = "block";
        }

        function closeProtectedForm() {
            document.getElementById("protectedForm").style.display = "none";
        }

        function getCheckedValues(name) {
            return Array.from(document.querySelectorAll(`input[name='${name}']:checked`))
                .map(el => el.value)
                .filter(v => v !== "ALL");
        }

        function selectAllLeagues(el) {
            document.querySelectorAll("input[name='league']").forEach(cb => cb.checked = el.checked);
        }

        function selectAllEligibleLeagues(el) {
            document.querySelectorAll("input[name='eligibleLeague']").forEach(cb => cb.checked = el.checked);
        }

        function getOriginsWithConfidence() {
            const originData = [];
            const globalPrediction = parseInt(document.querySelector("input[name='globalPrediction']:checked").value);

            document.querySelectorAll("input[name='pickOrigin']:checked").forEach(cb => {
                const container = cb.parentNode;
                const input = container.querySelector("input.origin-confidence");
                const conf = input && input.value.trim() ? parseFloat(input.value.trim()) : null;

                originData.push({
                    name: cb.value,
                    confidence: conf,
                    prediction: globalPrediction  // shared for all selected experts
                });
            });

            return originData;
        }


        function submitObject() {
            const odds = document.getElementById("popupOdds").value.trim();
            const origin_data = getOriginsWithConfidence();  // ⬅️ new per-origin confidence collection
            const league = getCheckedValues("league");
            const reusable = document.getElementById("popupReusable").checked;
            const mutualExclusion = parseInt(document.getElementById("popupMutualExclusion").value);
            const capital_limit = parseInt(document.getElementById("popupCapitalLimit").value);
            const pickType = document.querySelector("input[name='pickType']:checked")?.value;
            const playerTeam = document.getElementById("playerTeamInput").value.trim();
            const statType = document.querySelector("input[name='statType']:checked")?.value || "MoneyLine";
            const predictionValue = document.querySelector("input[name='globalPrediction']:checked")?.value;
            const totalCapital = parseFloat(document.getElementById("totalCapitalInput").value) || 0;





            if (!odds || origin_data.length === 0 || league.length === 0) {
                return alert("Please complete all fields and select at least one origin and one league.");
            }

            const payload = {
                id: editingPickId,
                name: tempName,
                odds,
                pick_origin: origin_data,  
                league,
                reusable,
                capital_limit,
                mutual_exclusion: mutualExclusion,
                pick_type: pickType,
                player_team: playerTeam || "None",
                stat_type: statType,
                prediction: parseInt(predictionValue)

            };

            const endpoint = isEditing ? "/edit" : "/process";

            fetch(endpoint, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(payload)
            })
            .then(response => response.json())
            .then(data => {
                updateButtons(data.objects);
                closePopup();
                isEditing = false;
                editingPickId = null;
                document.getElementById("responseOutput").innerText = data.response || "Updated.";
            });
        }


        function submitBoostPromo() {
            const boost_percentage = parseInt(document.getElementById("boostPercentage").value);
            const required_picks = parseInt(document.getElementById("requiredPicks").value);
            const same_sport = document.getElementById("sameSport").checked;

            fetch("/create_boost_promo", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ boost_percentage, required_picks, same_sport })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById("responseOutput").innerText = data.response;
                closeBoostForm();
            });
        }

        function submitProtectedPromo() {
            const protected_amount = parseInt(document.getElementById("protectedAmount").value);
            const eligible_leagues = getCheckedValues("eligibleLeague");

            fetch("/create_protected_promo", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ protected_amount, eligible_leagues })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById("responseOutput").innerText = data.response;
                closeProtectedForm();
            });
        }

        // function updateButtons(objects) {
        //     const container = document.getElementById("objectButtonsContainer");
        //     container.innerHTML = "";

        //     objects.forEach(obj => {
        //         const buttonWrapper = document.createElement("div");
        //         // buttonWrapper.style.display = "flex";
        //         // buttonWrapper.style.flexDirection = "column";
        //         buttonWrapper.style.gap = "2px";

        //         const btn = document.createElement("button");

        //         const name = obj.name || "Unnamed";
        //         const conf = obj.confidence !== null && obj.confidence !== undefined ? obj.confidence : "N/A";
        //         const odds = obj.odds || "N/A";
        //         const bayes_prob = obj.bayesian_prob || "N/A";
        //         const logi_prob = obj.logistic_prob || "N/A";
        //         const bayes_conf = obj.bayesian_conf || "N/A";
        //         const logisticWeight = 1 - bayes_conf;


        //         // Horizontal model influence bar
        //         const blendBar = document.createElement("div");
        //         blendBar.style.height = "6px";
        //         blendBar.style.width = "100%";
        //         blendBar.style.display = "flex";
        //         blendBar.style.border = "1px solid #000";
        //         blendBar.style.borderRadius = "2px";
        //         blendBar.style.overflow = "hidden";

        //         // Purple = Bayesian
        //         const bayesDiv = document.createElement("div");
        //         bayesDiv.style.width = `${bayes_conf * 100}%`;
        //         bayesDiv.style.backgroundColor = "purple";

        //         // Gold = Logistic
        //         const logisticDiv = document.createElement("div");
        //         logisticDiv.style.width = `${logisticWeight * 100}%`;
        //         logisticDiv.style.backgroundColor = "gold";



        //         btn.innerText = `${name} | odds: ${odds} | α Model: ${bayes_prob} | β Model: ${logi_prob} → Score: ${conf} `;
        //         btn.style.backgroundColor = getConfidenceColor(conf);
        //         btn.onclick = () => alert(`You clicked on ${obj.name}`);
        //         buttonWrapper.appendChild(btn);

        //         blendBar.appendChild(bayesDiv);
        //         blendBar.appendChild(logisticDiv);
        //         buttonWrapper.appendChild(blendBar);

        //         const editBtn = document.createElement("button");
        //         editBtn.innerText = "Edit";
        //         editBtn.onclick = () => editObject(obj.id, obj);
        //         buttonWrapper.appendChild(editBtn);

        //         const deleteBtn = document.createElement("button");
        //         deleteBtn.innerText = "Delete";
        //         deleteBtn.onclick = () => deleteObject(obj.id);
        //         buttonWrapper.appendChild(deleteBtn);

        //         container.appendChild(buttonWrapper);
        //     });
        // }

        function updateButtons(objects) {
            const container = document.getElementById("objectButtonsContainer");
            container.innerHTML = "";

            objects.forEach(obj => {
                const buttonWrapper = document.createElement("div");
                buttonWrapper.style.display = "flex";
                buttonWrapper.style.alignItems = "center";
                buttonWrapper.style.gap = "8px";
                buttonWrapper.style.marginBottom = "6px";
                buttonWrapper.style.flexWrap = "nowrap";

                const name = obj.name || "Unnamed";
                const conf = obj.confidence !== null && obj.confidence !== undefined ? obj.confidence : "N/A";
                const odds = obj.odds || "N/A";
                const bayes_prob = obj.bayesian_prob || "N/A";
                const logi_prob = obj.logistic_prob || "N/A";
                const bayes_conf = obj.bayesian_conf || 0.5;
                const logisticWeight = 1 - bayes_conf;

                // Create pick button (styled div)
                const pickBtn = document.createElement("div");
                pickBtn.className = "pick-button";
                pickBtn.style.backgroundColor = getConfidenceColor(conf);
                pickBtn.style.border = "1px solid black";
                pickBtn.style.padding = "8px 12px 14px";
                pickBtn.style.display = "inline-block";
                pickBtn.style.position = "relative";
                pickBtn.style.fontFamily = "monospace";
                pickBtn.style.fontSize = "14px";
                pickBtn.style.whiteSpace = "nowrap";
                pickBtn.style.overflow = "hidden";
                pickBtn.style.flexGrow = "1";
                pickBtn.style.minWidth = "0";
                pickBtn.style.maxWidth = "100%";
                pickBtn.style.cursor = "pointer";

                // Create prediction text
                const textLine = document.createElement("div");
                textLine.innerHTML = `
                    <b>${name}</b><br>
                    odds: ${odds} | α Model: ${bayes_prob} | β Model: ${logi_prob} → <b>Score: ${conf}</b> 
                    `;   
                    pickBtn.appendChild(textLine);

                // Create contribution bar
                const blendBar = document.createElement("div");
                blendBar.style.height = "6px";
                blendBar.style.width = "100%";
                blendBar.style.display = "flex";
                blendBar.style.border = "1px solid #000";
                blendBar.style.borderRadius = "2px";
                blendBar.style.overflow = "hidden";
                blendBar.style.marginTop = "4px";

                const bayesDiv = document.createElement("div");
                bayesDiv.style.width = `${bayes_conf * 100}%`;
                bayesDiv.style.backgroundColor = "purple";

                const logisticDiv = document.createElement("div");
                logisticDiv.style.width = `${logisticWeight * 100}%`;
                logisticDiv.style.backgroundColor = "gold";

                blendBar.appendChild(bayesDiv);
                blendBar.appendChild(logisticDiv);
                pickBtn.appendChild(blendBar);

                pickBtn.onclick = () => alert(`You clicked on ${obj.name}`);
                buttonWrapper.appendChild(pickBtn);

                // Edit button
                const editBtn = document.createElement("button");
                editBtn.innerText = "Edit";
                editBtn.style.flexShrink = "0";
                editBtn.onclick = () => editObject(obj.id, obj);
                buttonWrapper.appendChild(editBtn);

                // Delete button
                const deleteBtn = document.createElement("button");
                deleteBtn.innerText = "Delete";
                deleteBtn.style.flexShrink = "0";
                deleteBtn.onclick = () => deleteObject(obj.id);
                buttonWrapper.appendChild(deleteBtn);

                container.appendChild(buttonWrapper);
            });
        }






        function editObject(id, currentObj) {
            isEditing = true;
            editingPickId = id;
            tempName = currentObj.name;

            document.getElementById("popupNameDisplay").innerText = currentObj.name;
            document.getElementById("popupOdds").value = currentObj.odds;
            document.getElementById("popupCapitalLimit").value = currentObj.capital_limit;
            document.getElementById("popupReusable").checked = currentObj.reusable;
            document.getElementById("popupMutualExclusion").value = currentObj.mutual_exclusion || -1;

            // Set pick origin checkboxes and confidence
            document.querySelectorAll("input[name='pickOrigin']").forEach(box => {
                const originName = box.value;
                const match = currentObj.pick_origin.find(o =>
                    typeof o === "object" ? o.name === originName : o === originName
                );

                box.checked = !!match;
                box.dispatchEvent(new Event("change")); // ✅ trigger input field display

                const container = box.parentNode;
                const input = container.querySelector("input.origin-confidence");

                if (match && typeof match === "object" && input) {
                    input.value = match.confidence !== undefined && match.confidence !== null ? match.confidence : "";
                }
            });

            // Set league checkboxes
            document.querySelectorAll("input[name='league']").forEach(box => {
                box.checked = currentObj.league.includes(box.value);
            });

            document.getElementById("popupForm").style.display = "block";
            attachConfidenceListeners();

        }




        function deleteObject(id) {
            fetch("/delete", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ id: id })
            })
            .then(response => response.json())
            .then(data => updateButtons(data.objects));
        }

        function loadSamplePicks() {
            fetch("/load_sample_picks", {
                method: "POST"
            })
            .then(res => res.json())
            .then(data => {
                updateButtons(data.objects);
                document.getElementById("responseOutput").innerText = data.message;
                document.getElementById("optimizerSummary").innerText = "";  // clear old result
            });
        }

        function updateAccuracy() {
            const accuracy = document.getElementById("accuracyInput").value.trim();

            fetch("/update_accuracy", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ accuracy: accuracy })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById("accuracyValue").innerText = data.user_accuracy;
            });
        }

        function clearPicks() {
            if (!confirm("Are you sure you want to clear all picks?")) return;

            fetch("/clear_picks", {
                method: "POST"
            })
            .then(res => res.json())
            .then(data => {
                updateButtons(data.objects);  // Wipes the pick buttons
                document.getElementById("responseOutput").innerText = data.message;
                document.getElementById("optimizerSummary").innerText = "";  // Clear optimizer display
            });
        }

        function getConfidenceColor(score) {
            score = Math.max(0, Math.min(score, 100));
            const t = Math.pow(score / 100, 2.9);
            const r = Math.round((1 - t) * 210 + t * 0);
            const g = Math.round((1 - t) * 180 + t * 200);
            const b = Math.round((1 - t) * 140 + t * 0);
            return `rgb(${r}, ${g}, ${b})`;
        }

        // Load all picks on initial page load
        window.onload = () => {
        fetch("/get_picks")
        .then(res => res.json())
        .then(data => updateButtons(data.objects));
        };
    </script>
</body>
</html>
