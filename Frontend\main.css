/* Basic Reset */
body, p, ul {
    margin: 0;
    padding: 0;
    margin-bottom: 20px; /* Adds space below each paragraph */
}

body h1 {
    margin: 0;
    font-size: 75px;  /* Larger font size for the main header title */
}

body h3 {
    margin: 0;
    font-size: 30px;  /* Larger font size for the main header title */
}

body {
    font-family: Arial, sans-serif;
    color: #041608;
    background-color: black;  /* Sets the background color to black */
    text-align: center;
    line-height: 1.6;
    padding: 20px;
    background-image: url('images/ProfBackground.png'); /* Path to your image */
    background-size: cover; /* Cover ensures the image covers the entire viewport */
    background-position: center; /* Center the image on the page */
    background-attachment: fixed; /* Optional: The image will not scroll with the page */
    background-repeat: no-repeat; /* Prevents the image from repeating */
}

.container section, .container2 section {
    position: relative; /* Positioned relative to their normal position */
    margin-top: 30px; /* Space between sections */
}

#gradient-header {
    background-color: rgba(255, 255, 255, 0.5); /* Semi-transparent white background */
    backdrop-filter: grayscale(100%); /* Makes everything behind it grayscale */
    width: 80%; /* Adjust as necessary */
    border: 3px solid rgb(18, 50, 179); /* border */
    padding: 20px;
    text-align: center;
    font-size: 24px;
    color: rgb(18, 50, 179); /* Text color, adjust as needed */
    margin: 9%; /* Optional, for spacing around the header */
    border-radius: 10px;

    text-shadow: 
        -1px -1px 0 #000,  
         1px -1px 0 #000,
        -1px  1px 0 #000,
         1px  1px 0 #000; /* Black outline */
}

a:visited{ color: yellow;
}


nav ul li a {
    display: flex;
    flex-direction: column; /* Stack image and text */
    align-items: center; /* Center-align items */
    text-decoration: none;
    color: white; /* White text */
    padding: 10px;
    border: 1px solid white; /* Optional border */
    border-radius: 5px; /* Rounded corners for buttons */
}

nav ul li a img {
    vertical-align: middle; /* Align image vertically */
    margin-right: 8px; /* Space between image and text */
    width: 35px; /* Width of the image, adjust as needed */
    height: 35px; /* Height of the image, adjust as needed */
    border-radius: 10px; /* Gives the image rounded corners */
    /* Use a percentage to make it fully round, or use pixels for slight rounding */
}

nav ul li a:hover {
    background-color: #ddd;
    color: black;
}

body {
    padding-left: 100px; /* Adjust based on header width to avoid content overlap */
}

#sidebar {
    position: fixed; /* Sticky position */
    display: flex; /* Enable flexbox */ 
    top: 0; /* Align top */
    left: 0; /* Align left */
    width: 100px; /* Width of the sidebar */
    height: 100vh; /* Full height of the viewport */
    background-color: #230e0ede; /* Background color of the sidebar */
    z-index: 10; /* Stacks on top */
}

#sidebar ul {
    list-style-type: none; /* Remove default list style */
    padding: 0; /* Remove default padding */
    margin: 0; /* Remove default margins */
}

#sidebar ul li {
    margin-top: 5px; /* Adjust the value as needed for spacing above the button */
    margin-bottom: 5px; /* Adjust the value as needed for spacing below the button */
}

#sidebar ul li a {
    display: flex; /* Enable flexbox */ 
    padding: 10px; /* Spacing inside each link */
    color: #fff; /* Text color */
    text-decoration: none; /* No underline */
    border-bottom: 3px solid rgb(18, 50, 179) /* Separator between buttons */
}

#sidebar ul li a img {
    vertical-align: middle; /* Align image with text */
    margin-right: 8px; /* Space between image and text */
    border-radius: 5px; /* Rounded corners for images */
    width: 24px; /* Image width */
    height: 24px; /* Image height */
}

.wrapper {
    display: flex; /* This will align children (containers) side by side */
    justify-content: space-between; /* This puts space between your containers */
    align-items: flex-start; /* This aligns containers to the top */
    gap: 30px; /* This adds space between your containers */
}

#aboutPic img {
    vertical-align: middle; /* Align image vertically */
    margin-right: 8px; /* Space between image and text */
    width: 50%; /* Width of the image, adjust as needed */
    height: 50%; /* Height of the image, adjust as needed */
    border-radius: 15px; /* Gives the image rounded corners */
    /*box-shadow: 0 0 10px rgb(208, 180, 23);*/
    border: 2px solid black; 

}

#experiencePic img {
    vertical-align: middle; /* Align image vertically */
    margin-right: 8px; /* Space between image and text */
    margin-top: 60px;
    margin-bottom: 60px;
    width: 75%; /* Width of the image, adjust as needed */
    height: auto; /* Height of the image, adjust as needed */
    border-radius: 10%; /* Gives the image rounded corners */
    box-shadow: 0 0 10px black;
    border: 2px solid rgb(18, 50, 179); 

}

#experiencePic2 img {
    vertical-align: middle; /* Align image vertically */
    margin-right: 8px; /* Space between image and text */
    margin-top: 15px;
    width: 50%; /* Width of the image, adjust as needed */
    height: auto; /* Height of the image, adjust as needed */
    border-radius: 10%; /* Gives the image rounded corners */
    box-shadow: 0 0 10px black;
    border: 2px solid rgb(18, 50, 179); 

}

#educationPic img {
    vertical-align: middle; /* Align image vertically */
    margin-right: 8px; /* Space between image and text */
    margin-top: 15px;
    margin-bottom: 15px;
    width: 60%; /* Width of the image, adjust as needed */
    height: 50%; /* Height of the image, adjust as needed */
    border-radius: 10%; /* Gives the image rounded corners */
    box-shadow: 0 0 10px black;
    border: 2px solid rgb(18, 50, 179); 

}


#aboutPic h2{
    font-size: 24px;
    color: rgb(255, 255, 255);
    margin: 9%; /* Optional, for spacing around the header */

    text-shadow: 
        -2px -2px 0 #000,  
         2px -2px 0 #000,
        -2px  2px 0 #000,
         2px  2px 0 #000; /* Black outline */
}

#about {
    margin-left: auto; /* Adjust as needed */
    margin-right: auto;
    margin-top: 30px;
    padding-left: 30px; /* Adjust based on header width to avoid content overlap */
    padding-right: 30px;
    box-shadow: 0 0 10px rgb(18, 50, 179);
    background-color: black; /* Sets the background to black */
    border: 2px solid rgb(18, 50, 179); 
    transition: all 0.2s ease; /* Smooth transition for hover effect */
    color: rgb(229, 239, 228);
    border-radius: 10px;
}


.container section:nth-child(even) {
    margin-left: auto; /* Adjust as needed */
    margin-right: auto;
    margin-top: 30px;
    padding-left: 30px; /* Adjust based on header width to avoid content overlap */
    padding-right: 30px;
    box-shadow: 0 0 10px rgb(18, 50, 179);
    background-color: black; /* Sets the background to black */
    border: 2px solid rgb(18, 50, 179); 
    transition: all 0.2s ease; /* Smooth transition for hover effect */
    color: rgb(229, 239, 228);
    border-radius: 10px;
}

.container section:nth-child(odd) {
    margin-right: auto; /* Adjust as needed */
    margin-left: auto;
    margin-top: 30px;
    padding-left: 30px; /* Adjust based on header width to avoid content overlap */
    padding-right: 30px;
    box-shadow: 0 0 10px rgba(168, 27, 27, 0);;
    background-color: rgba(0, 0, 0, 0); 
    border: 2px solid rgba(0, 0, 0, 0); 
    transition: all 0.2s ease; /* Smooth transition for hover effect */
    color: rgb(229, 239, 228);
    border-radius: 10px;
}

.container2 section:nth-child(odd) {
    margin-left: auto; /* Adjust as needed */
    margin-right: auto;
    margin-top: 30px;
    padding-left: 30px; /* Adjust based on header width to avoid content overlap */
    padding-right: 30px;
    box-shadow: 0 0 10px rgb(18, 50, 179);
    background-color: black; /* Sets the background to black */
    border: 2px solid rgb(18, 50, 179); /* Sets the border color to green */
    transition: all 0.2s ease; /* Smooth transition for hover effect */
    color: rgb(229, 239, 228);
    border-radius: 10px;
    z-index: 20;
}

.container2 section:nth-child(even) {
    margin-right: auto; /* Adjust as needed */
    margin-left: auto;
    margin-top: 30px;
    padding-left: 30px; /* Adjust based on header width to avoid content overlap */
    padding-right: 30px;
    box-shadow: 0 0 10px rgba(168, 27, 27, 0);;
    background-color: rgba(0, 0, 0, 0); 
    border: 2px solid rgba(0, 0, 0, 0); 
    transition: all 0.2s ease; /* Smooth transition for hover effect */
    color: rgb(229, 239, 228);
    border-radius: 10px;
    z-index: 20;
}

section{
    margin-left: auto; /* Adjust as needed */
    margin-right: auto;
    margin-top: 30px;
    padding-left: 30px; /* Adjust based on header width to avoid content overlap */
    padding-right: 30px;
    box-shadow: 0 0 10px rgb(18, 50, 179);
    background-color: black; /* Sets the background to black */
    border: 2px solid rgb(18, 50, 179); /* Sets the border color to green */
    transition: all 0.2s ease; /* Smooth transition for hover effect */
    color: rgb(229, 239, 228);
    border-radius: 10px;
    z-index: 20;
}



    
.container2 section:nth-child(odd):hover {
    /* Create a glossy/shiny effect using a light gradient */
    background-image: linear-gradient(rgba(251, 244, 187, 0.2), rgba(255, 255, 255, 0.1));

    box-shadow: 0 0 15px rgba(238, 156, 25, 0.624); 
    border-color: rgb(229, 186, 58); /* Brighter green border on hover */
}

.container section:nth-child(even):hover {
    /* Create a glossy/shiny effect using a light gradient */
    background-image: linear-gradient(rgba(251, 244, 187, 0.2), rgba(255, 255, 255, 0.1));

    box-shadow: 0 0 15px rgba(238, 156, 25, 0.624); 
    border-color: rgb(229, 186, 58); /* Brighter green border on hover */
}


footer {
    padding-left: 100px; /* Adjust based on header width to avoid content overlap */
    text-align: center;
    padding: 10px 0;
    background-color: #333;
    color: white;
}

footer p a {
    color: #fff;
    text-decoration: none;
}

