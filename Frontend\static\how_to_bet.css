body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
}

#gradient-header {
    background-color: rgba(255, 255, 255, 0.5); /* Semi-transparent white background */
    backdrop-filter: grayscale(100%); /* Makes everything behind it grayscale */
    width: 80%; /* Adjust as necessary */
    border: 3px solid rgb(18, 50, 179); /* border */
    text-align: center;
    font-size: 24px;
    margin: 9%;
    color: Black; /* Text color, adjust as needed */
    border-radius: 10px;
    padding-left: -38%;

    text-shadow: 
        -1px -1px 0 #000,  
         1px -1px 0 #000,
        -1px  1px 0 #000,
         1px  1px 0 #000; /* Black outline */
}

a:visited{ color: yellow;
}


nav ul li a {
    display: flex;
    flex-direction: column; /* Stack image and text */
    align-items: center; /* Center-align items */
    text-decoration: none;
    color: white; /* White text */
    background-color: black;
    padding: 10px;
    border: 1px solid blue; /* Optional border */
    border-radius: 5px; /* Rounded corners for buttons */
}

nav ul li a img {
    vertical-align: middle; /* Align image vertically */
    margin-right: 8px; /* Space between image and text */
    width: 35px; /* Width of the image, adjust as needed */
    height: 35px; /* Height of the image, adjust as needed */
    border-radius: 10px; /* Gives the image rounded corners */
    /* Use a percentage to make it fully round, or use pixels for slight rounding */
}

nav ul li a:hover {
    background-color: #ddd;
    color: black;
}

body {
    padding-left: 30%; /* Adjust based on header width to avoid content overlap */
}

#sidebar {
    position: fixed; /* Sticky position */
    display: flex; /* Enable flexbox */ 
    top: 0; /* Align top */
    left: 0; /* Align left */
    width: 100px; /* Width of the sidebar */
    height: 100vh; /* Full height of the viewport */
    background-color: #230e0ede; /* Background color of the sidebar */
    z-index: 10; /* Stacks on top */
}

#sidebar ul {
    list-style-type: none; /* Remove default list style */
    padding: 0; /* Remove default padding */
    margin: 0; /* Remove default margins */
}

#sidebar ul li {
    margin-top: 5px; /* Adjust the value as needed for spacing above the button */
    margin-bottom: 5px; /* Adjust the value as needed for spacing below the button */
}

#sidebar ul li a {
    display: flex; /* Enable flexbox */ 
    padding: 10px; /* Spacing inside each link */
    color: #fff; /* Text color */
    text-decoration: none; /* No underline */
    border-bottom: 3px solid rgb(18, 50, 179) /* Separator between buttons */
}

#sidebar ul li a img {
    vertical-align: middle; /* Align image with text */
    margin-right: 8px; /* Space between image and text */
    border-radius: 5px; /* Rounded corners for images */
    width: 24px; /* Image width */
    height: 24px; /* Image height */
}

.grid-container {
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
}

.semester {
    flex: 0 1 calc(33.333% - 20px); /* Subtract margin */
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    margin: 10px;
    padding: 10px;
}

.semester h2 {
    font-size: 1.5em;
    color: #333;
    border-bottom: 1px solid #333;
    padding-bottom: 5px;
    margin-bottom: 10px;
}

.semester ul {
    list-style: none;
    padding: 0;
}

.semester ul li {
    padding: 5px 0;
    font-size: 1em;
    color: #666;
}

@media (max-width: 600px) {
    .semester {
        flex: 0 1 100%;
    }
}
